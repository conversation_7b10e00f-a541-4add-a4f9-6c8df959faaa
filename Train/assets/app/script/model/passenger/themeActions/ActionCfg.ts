import { PassengerLifeAnimation } from "../../../common/constant/Enums";
import { StateType } from "../StateEnum";

export enum ActType { //行为类型
    DEFAULT = 1000,

    //黑跳跳
    CHESS, //下棋

    //白跳跳
    RAP,

    //赏金猎犬
    PLAY_GUITAR, //弹吉他
    STARGAZE, //看望远镜

    //胡桃夹子喵
    GIGGLE, //傻笑

    //浪客喵
    STAND_SLEEP, //站着睡觉
    THINK, //思考

    //工程狮
    READ_BOOK, //看书

    //摇滚汪
    PLAY_BASS,

}

export const ActAnimCfg = {
    [ActType.CHESS]: { id: 1003, anim: PassengerLifeAnimation.SIT_ACT },
    [ActType.PLAY_GUITAR]: { id: 1005, anim: PassengerLifeAnimation.SIT_ACT },
    [ActType.STARGAZE]: { id: 1005, anim: PassengerLifeAnimation.TRAIN_HEAD_ACT },
    [ActType.GIGGLE]: { id: 1006, anim: PassengerLifeAnimation.STAND_ACT + "2" },
    [ActType.STAND_SLEEP]: { id: 1007, anim: PassengerLifeAnimation.STAND_ACT },
    [ActType.THINK]: { id: 1007, anim: PassengerLifeAnimation.TRAIN_HEAD_ACT },
    [ActType.READ_BOOK]: { id: 1015, anim: PassengerLifeAnimation.SIT_ACT },
    [ActType.PLAY_BASS]: { id: 1009, anim: PassengerLifeAnimation.STAND_ACT },
}

export enum ActCondType {
    STAND = 1,
    SIT,
}

export const RoleActCfg = {
    1003: [ //黑跳跳
        { condType: ActCondType.STAND, act: { start: PassengerLifeAnimation.STAND_ACT }, weight: 1 }, //贴符
        {
            type: ActType.CHESS, condType: ActCondType.STAND, act: {  //下棋
                loop: {
                    anim: "life/standAct2",
                    count: [{num: 1, weight: 1},{num: 2, weight: 2},{num: 3, weight: 1}],
                },
                s: 100,
                loopEndAnims: ["life/standAct3"],
                end: [PassengerLifeAnimation.IDLE, PassengerLifeAnimation.IDLE],
            },
            weight: 2,
            random: false, //是否是随机动作
        },
    ],
    1004: [ //白跳跳
        {
            condType: ActCondType.STAND, act: { //rap
                start: PassengerLifeAnimation.STAND_ACT_START,
                count: [1, 3],
                loop: {
                    anim: PassengerLifeAnimation.STAND_ACT,
                    count: [1, 3],
                },
                end: PassengerLifeAnimation.STAND_ACT_END
            },
        },
    ],
    1005: [ //赏金猎犬
        { condType: ActCondType.STAND, act: { start: PassengerLifeAnimation.STAND_ACT }, weight: 1 }, //撇一眼
        { condType: ActCondType.STAND, act: { start: PassengerLifeAnimation.STAND_ACT }, weight: 1 }, //掏出悬赏令
        {
            type: ActType.PLAY_GUITAR, condType: ActCondType.SIT, act: { //弹吉他
                loop: {
                    anim: PassengerLifeAnimation.SIT_ACT,
                    count: [10, 15],
                },
            },
            weight: 3 
        },
    ],
    1009: [ //摇滚汪
        {
            type: ActType.PLAY_BASS, condType: ActCondType.STAND, act: { //弹bass
                start: PassengerLifeAnimation.STAND_ACT_START,
                count: [4, 6],
                loop: {
                    anim: PassengerLifeAnimation.STAND_ACT,
                    count: [2, 3],
                },
                s: 70,
                loopEndAnims: [PassengerLifeAnimation.STAND_ACT_END, `${PassengerLifeAnimation.STAND_ACT}2`, PassengerLifeAnimation.STAND_ACT_START],
                end: PassengerLifeAnimation.STAND_ACT_END
            }
        },
    ]
}

export const ActionCfg = {
    [StateType.IDLE]: [2, 3], //默认idle时间(秒)
    [StateType.EAT]: {
        //吃面包、水果之类的
        eat: [2, 3], //每次吃东西，总共会吃多少口
        munch: [2, 3], //咀嚼次数（每吃完一口，会咀嚼的次数）
        //吃饭（正餐）
        time: [10, 15], //循环动画，持续时间
    },
    [StateType.DRINK]: {
        //喝水
        drink: [2, 3], //每次喝水，总共会喝多少口
        taste: [1, 2], //品味次数（每喝完一口，会品味的次数）
    },
    [StateType.FISH]: [25, 40], //钓鱼时间
    [StateType.SIT]: [10, 15], //普通坐着时间 
    [StateType.SWING]: [10, 15], //坐摇摇 时间 
    [StateType.WATCH_TV]: [25, 30], //看电视时间
    [StateType.COOK]: [10, 15], //做饭时间
    [StateType.ENGINE_POWER_FIRST]: [10, 15], //动力室-赏金猎犬-旋转阀门
    [StateType.ENGINE_POWER_SECONE]: [10, 15], //动力室-胡桃夹子喵-扳手修理
    [StateType.WALK_TREADMILL]: [20, 35], //动力室-跑步机行走世间
    [StateType.ENGINE_PULL]: [20, 35], //动力室-拉力器使用时间
    [StateType.ENGINE_COMPUTER]: {//动力室-电脑工作, (工作->摸鱼) 循环
        work: [10, 15], //循环动画，每次工作的时间
        sleep: [10, 15], //循环动画，每次摸鱼的时间
        times: [2, 3], //循环次数
    },
    [StateType.ENGINE_NOTES]: {//动力室-记录笔记, (思考->记录) 循环
        write: [10, 15], //循环动画，每次写记录的时间
        times: [2, 3], //循环次数
    },

    //列车每日任务相关
    [StateType.REPAIR]: [10, 20], //单次修理时间
    [StateType.CLEAN]: [10, 20], //单次打扫时间

    [StateType.WATER_GAME]: [5, 10], //造水间-游戏
    [StateType.WATER_CRY]: [5, 10], //造水间-游戏哭哭
    [StateType.WATER_AIR]: [5, 10], //造水间-打气

    [StateType.TAKE_BATH]: [15, 30], //浴室-泡澡
    [StateType.DANCING]: [25, 30], //舞厅-跳舞

    [StateType.PLAY_ACT]: [5, 6], //默认表演时间，如果某个行为没配，默认用这个时间

    [ActType.STARGAZE]: [20, 30], //赏金猎犬-看望远镜
    [ActType.GIGGLE]: [5, 6], //胡桃夹子喵-傻笑
    [ActType.STAND_SLEEP]: [15, 25], //浪客喵-站立睡觉
    [ActType.THINK]: [10, 15], //浪客喵-插着手思索
    [ActType.READ_BOOK]: [15, 25], //工程狮-看书
}

const USE_BUILD_CD = [30, 60] //使用设施冷却时间，现实时间-秒
const USE_CARRIAGE_CD = [4, 4] //使用车厢冷却时间，模拟时间-小时

const WATER_OUTPUT_COUNT = [5, 10] //造水间，爆小珍珠的个数

const CARRIAGE_MAX_STAY_TIME = 180 //最多在车厢停留多久时间，单位秒

const PARTORL_CARRAIGE_TIME = [30, 60] // 日常任务在车厢停留的时间，单位秒

export {
    USE_BUILD_CD,
    USE_CARRIAGE_CD,
    WATER_OUTPUT_COUNT,
    CARRIAGE_MAX_STAY_TIME,
    PARTORL_CARRAIGE_TIME,
}