import { BUILD_MOUNT_POINT, PassengerLifeAnimation } from "../../../common/constant/Enums"
import { gameHelper } from "../../../common/helper/GameHelper"
import DormModel from "../../train/dorm/DormModel"
import { ActionNode } from "../ActionTree"
import { TimeStateData } from "../StateDataType"
import { StateType } from "../StateEnum"
import BaseAction from "./BaseAction"

export default class DormAction extends BaseAction {

    protected carriage: DormModel = null

    protected async start(action: ActionNode) {
        this.debug('start')

        await this.onBeforeStart(action)
        if (action.isOK()) return

        let cfgs = [
            { act: this.standPlayAct, check: this.checkStandAct, weight: 25 }, //站立表演
            { act: this.toRandomPos, weight: 25 }, //闲逛
            { act: this.toBuildPlay, weight: 75, check: this.checkPlay }, //和设施交互
        ]
        await this.runRandomAct(action, cfgs)
        await action.wait(ut.random(1, 3))
    }

    private isGuideTire() {
        return this.role.id == 1006 && gameHelper.guide.needForbidGoGarden2()
    }

    protected checkStandAct() {
        return super.checkStandAct() && !this.isGuideTire()
    }

    private checkPlay() {
        if (this.isGuideTire()) return false
        return this.checkLeftPlay() || this.checkRightBedPlay() || this.checkChairPlay()
    }

    private checkLeftPlay() {
        return this.checkBuildPlay(this.carriage.getLeftBed(), [0, 1])
    }

    private checkStairPlay() {
        return this.checkBuildPlay(this.carriage.getLeftBed(), [1])
    }

    private checkLeftBedPlay() {
        return this.checkBuildPlay(this.carriage.getLeftBed())
    }

    private checkRightBedPlay() {
        return this.checkBuildPlay(this.carriage.getRightBed())
    }

    private checkBedPlay() {
        let builds = this.carriage.getBeds()
        return builds.some(b => this.checkBuildPlay(b))
    }

    private checkChairPlay() {
        let builds = this.carriage.getChairs()
        return builds.some(b => this.checkBuildPlay(b))
    }

    //去睡觉
    protected async toSleep(action) {
        let leftBed = this.carriage.getLeftBed()
        let rightBed = this.carriage.getRightBed()
        let cfgs = [
        ]
        let anim = this.actionAgent.getAnim(PassengerLifeAnimation.SLEEP)
        if (anim) {
            if (leftBed && leftBed.canUse()) {
                cfgs.push({ act: this.toLeftBedSleep })
            }
            if (rightBed && rightBed.canUse()) {
                cfgs.push({ act: this.toRightBedSleep })
            }
            if (cfgs.length <= 0) {
                let build = this.getUseChair(false)
                if (build) {
                    cfgs.push({ act: this.toChairSleep, params: { build } })
                }
            }
        }

        if (cfgs.length <= 0) {
            cfgs.push({ act: this.sleep, params: { anim: PassengerLifeAnimation.STAND_SLEEP } })
        }
        await this.runRandomAct(action, cfgs)
        action.ok()
    }

    //去和设施交互
    private async toBuildPlay(action) {
        // TODO: 10 10 10
        let cfgs = [
            { act: this.toStairPlay, check: this.checkStairPlay, weight: 1 }, //去楼梯交互
            { act: this.toBedPlay, check: this.checkBedPlay, weight: 98 }, //去床交互
            { act: this.toChairPlay, check: this.checkChairPlay, weight: 1 }, //去凳子交互
        ]

        await this.runRandomAct(action, cfgs)
        action.ok()
    }


    private async toBedPlay(action) {
        let build = this.getUseBed()
        let cfgs = [
            // TODO: 20 40 40
            { act: this.toBedSit, weight: 1 }, //坐着
            { act: this.toBedPlayAct, check: this.checkSitAct, weight: 1 }, //弹吉他
            { act: this.toBedPlaySwing, check: this.checkSitAct, weight: 98 }, //坐摇摇
        ]
        await this.runRandomAct(action, cfgs, { build })
        this.actionAgent.addUseBuildRecord(build, 0)
        action.ok()
    }

    //------------left bed --------------------------
    private async toLeftBedSleep(action) {
        let build = this.carriage.getLeftBed()
        let index = 0
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.toEnterLeftBed)
        let sleepParams = Object.assign({ build }, action.params)
        await action.run(this.sleep, sleepParams)
        await action.run(this.toExitLeftBed)
        build.setUseLock(false, index, this.role.id)
        action.onTerminate = null
        await action.run(this.toEmptyPosFromLeftBed)
        action.ok()
    }

    //随机上左边床的方式
    private async toEnterLeftBed(action) {
        let cfgs = [
            { act: this.enterLeftBed },
            {
                act: this.enterLeftBedByStair, check: () => {
                    return this.actionAgent.getAnim(PassengerLifeAnimation.TO_LEFT_BED)
                }
            },
        ]
        await this.runRandomAct(action, cfgs)
        action.ok()
    }

    //普通地跳上床
    private async enterLeftBed(action) {
        this.debug("enterLeftBed")
        let params = Object.assign({ getDown: true }, action.params)
        let build = this.carriage.getLeftBed()
        let actionAgent = this.actionAgent

        await action.run(this.moveToBuild, { build, paths: [{ index: 3 }, { index: 4, force: true }] })

        let { time, jumpTime } = this.getJumpInfo()
        let getDown = params.getDown
        let getDownAnim = actionAgent.getAnim(PassengerLifeAnimation.GET_DOWN)
        if (!getDownAnim) {
            getDown = false
        }
        if (getDown && getDownAnim) {
            time += getDownAnim.duration
        }
        if (time <= 0) return action.ok()
        action.onTerminate = () => {
            this.actionAgent.popState(StateType.ENTER_BED)
        }
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(StateType.ENTER_BED, { build, timeData, jumpTime, getDown })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    //走楼梯上床
    private async enterLeftBedByStair(action) {
        let build = this.carriage.getLeftBed()

        await action.run(this.moveToBuild, { build, paths: [{ index: 1 }, { index: 2, force: true }] })

        let time = this.actionAgent.getAnimsTime([
            PassengerLifeAnimation.TO_LEFT_BED,
        ])
        this.debug("enterLeftBedByStair", time)
        action.onTerminate = () => {
            this.actionAgent.popState(StateType.ENTER_LEFT_BED_BY_STAIR)
        }
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(StateType.ENTER_LEFT_BED_BY_STAIR, { build, timeData })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    private async toEmptyPosFromLeftBed(action) {
        let build = this.carriage.getLeftBed()
        let pos = build.getUsePos(3)
        await action.run(this.forceMove, pos)
        action.ok()
    }

    private async toExitLeftBed(action) {
        let build = this.carriage.getLeftBed()
        let pos = build.getUsePos(4)
        this.role.setPosition(pos)
        await action.run(this.exitBed, Object.assign({ build, pos }, action.params))
        action.ok()
    }

    private async exitBed(action: ActionNode) {
        let params = Object.assign({ getUp: true }, action.params)
        let actionAgent = this.actionAgent
        let { build, pos } = params
        let getUp = params.getUp

        let { time, downTime } = this.getDownInfo()
        let getUpAnim = actionAgent.getAnim(PassengerLifeAnimation.GET_UP)
        if (!getUpAnim) {
            getUp = false
        }
        if (getUp && getUpAnim) {
            time += getUpAnim.duration
        }
        if (time <= 0) return action.ok()
        action.onTerminate = () => {
            this.actionAgent.popState(StateType.EXIT_BED)
        }
        this.debug("exitBed", time)
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(StateType.EXIT_BED, { build, timeData, downTime, pos, getUp })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    private getBedActFunc(build) {
        let commonAct
        if (build == this.carriage.getLeftBed()) {
            commonAct = this.toLeftBedAct
        }
        else if (build == this.carriage.getRightBed()) {
            commonAct = this.toRightBedAct
        }
        else {
            console.error("getBedActFunc", build)
            return (action) => {
                action.ok()
            }
        }
        return commonAct
    }

    private getUseBed(isPlay = true) {
        return this.getUseBuild(this.carriage.getBeds(), isPlay)
    }

    private async toBedSit(action) {
        let { build } = action.params
        await this.getBedActFunc(build).call(this, action, async (subAction: ActionNode) => {
            Object.assign(subAction.params, { mountPoint: BUILD_MOUNT_POINT.SLEEP })
            await this.sit(subAction)
        })
    }

    private async toBedPlayAct(action) {
        let { build } = action.params
        await this.getBedActFunc(build).call(this, action, async (subAction) => {
            Object.assign(subAction.params, { mountPoint: BUILD_MOUNT_POINT.SLEEP })
            await this.sit(subAction)
        })
    }

    private async toBedPlaySwing(action) {
        let { build } = action.params
        await this.getBedActFunc(build).call(this, action, async (subAction) => {
            Object.assign(subAction.params, { mountPoint: BUILD_MOUNT_POINT.SLEEP })
            await this.swing(subAction)
        })
    }


    private async toLeftBedAct(action, act) {
        this.debug("toLeftBedPlay")
        let index = 0
        let build = this.carriage.getLeftBed()
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.enterLeftBed, { getDown: false })
        await action.run(act, Object.assign(action.params || {}, { build }))
        await action.run(this.toExitLeftBed, { getUp: false })
        action.onTerminate()
        action.onTerminate = null
        await action.run(this.toEmptyPosFromLeftBed)
        action.ok()
    }

    // 楼梯交互
    private async toStairPlay(action) {
        let cfgs = [
            { act: this.toStairSit, weight: 20 }, //坐着
            { act: this.toStairPlayAct, check: this.checkSitAct, weight: 40 }, //弹吉他
            { act: this.toStairDrink, check: this.checkSitDrink, weight: 40 }, //喝茶
        ]
        await this.runRandomAct(action, cfgs)
        this.actionAgent.addUseBuildRecord(this.carriage.getLeftBed(), 1)
        action.ok()
    }

    // 去楼梯坐着
    private async toStairSit(action) {
        await this.toStairAct(action, this.sit)
    }

    // 楼梯演奏
    private async toStairPlayAct(action) {
        await this.toStairAct(action, this.sitPlayAct)
    }

    // 楼梯喝茶
    private async toStairDrink(action) {
        await this.toStairAct(action, this.sitDrink)
    }

    private async toStairAct(action, act) {
        let build = this.carriage.getLeftBed()
        let index = 1
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.moveToBuild, { build, paths: [{ index: 0 }] })
        await action.run(this.enterStair)
        await action.run(act, Object.assign(action.param || {}, { build }))
        await action.run(this.exitStair)
        action.onTerminate()
        action.ok()
    }

    private async enterStair(action) {
        this.debug("enterStair")
        let build = this.carriage.getLeftBed()
        let { time, jumpTime } = this.getJumpInfo(false)
        action.onTerminate = () => {
            this.actionAgent.popState(StateType.ENTER_STAIR)
        }
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(StateType.ENTER_STAIR, { build, timeData, jumpTime })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    private async exitStair(action) {
        this.debug("exitStair")
        let build = this.carriage.getLeftBed()
        let pos = build.getUsePos(0)
        let { time, downTime } = this.getDownInfo()
        let timeData = new TimeStateData().init(time)
        action.onTerminate = () => {
            this.actionAgent.popState(StateType.EXIT_STAIR)
        }
        this.actionAgent.pushState(StateType.EXIT_STAIR, { build, timeData, downTime, pos })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }
    //---------------------------------------------


    //------------------ right bed ----------------------------
    private async toRightBedSleep(action) {
        let index = 0
        let build = this.carriage.getRightBed()
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.toEnterRightBed)
        if (build.isDown()) {
            build.toUp()
        }
        let sleepParams = Object.assign({ build }, action.params)
        await action.run(this.sleep, sleepParams)
        await action.run(this.toExitRightBed)
        action.onTerminate()
        action.ok()
    }

    private async toEnterRightBed(action) {
        let bookcase = this.carriage.getBookcase()
        let cfgs = [
            { act: this.enterRightBed, weight: 25 }, //普通上床
            {
                act: this.enterRightBedByBookcase, check: () => {
                    return !!bookcase && this.actionAgent.getAnim(PassengerLifeAnimation.TO_RIGHT_BED)
                }, weight: 75
            } //走书柜上床
        ]
        await this.runRandomAct(action, cfgs)
        action.ok()
    }

    //走书柜上床
    private async enterRightBedByBookcase(action) {
        let build = this.carriage.getRightBed()

        await action.run(this.moveToBuild, { build, paths: [{ index: 0 }] })

        let time = this.actionAgent.getAnimsTime([
            PassengerLifeAnimation.TO_RIGHT_BED,
        ])
        this.debug("enterRightBedByBookcase", time)
        action.onTerminate = () => {
            this.actionAgent.popState(StateType.ENTER_RIGHT_BED_BY_BOOKCASE)
        }
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(StateType.ENTER_RIGHT_BED_BY_BOOKCASE, { build, timeData })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    //普通地跳上床
    private async enterRightBed(action: ActionNode) {
        let build = this.carriage.getRightBed()
        let actionAgent = this.actionAgent

        await action.run(this.moveToBuild, { build, paths: [{ index: 1 }] })
        await action.run(build.down, null, build)

        let params = Object.assign({ getDown: true }, action.params)
        let getDown = params.getDown
        let { time, jumpTime } = this.getJumpInfo()
        let getDownAnim = actionAgent.getAnim(PassengerLifeAnimation.GET_DOWN)
        if (!getDownAnim) {
            getDown = false
        }
        if (getDown && getDownAnim) {
            time += getDownAnim.duration
        }
        this.debug("enterRightBed", time)
        if (time <= 0) return action.ok()
        action.onTerminate = () => {
            this.actionAgent.popState(StateType.ENTER_BED)
        }
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(StateType.ENTER_BED, { build, timeData, jumpTime, getDown })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    private async toExitRightBed(action) {
        let build = this.carriage.getRightBed()
        let pos = build.getUsePos(1)
        this.role.setPosition(pos)
        await action.run(this.exitBed, Object.assign({ build, pos }, action.params))
        if (build.isDown()) {
            build.toUp()
        }
        action.ok()
    }

    private async toRightBedAct(action, act) {
        this.debug("toLeftBedPlay")
        let index = 0
        let build = this.carriage.getRightBed()
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.enterRightBed, { getDown: false })
        await action.run(act, Object.assign(action.params || {}, { build }))
        await action.run(this.toExitRightBed, { getUp: false })
        action.onTerminate()
        action.ok()
    }
    //---------------------------------------------------


    //-------------------- chair -----------------------------
    // 凳子交互
    private async toChairPlay(action) {
        let build = this.getUseChair()
        let cfgs = [
            { act: this.toChairSit, weight: 40 }, //坐着
            { act: this.toChairEat, check: this.checkSitEat, weight: 10 }, //吃东西
            { act: this.toChairDrink, check: this.checkSitDrink, weight: 10 }, //喝饮料
            { act: this.toChairPlayAct, check: this.checkSitAct, weight: 40 }, //弹吉他
        ]
        await this.runRandomAct(action, cfgs, { build })
        this.actionAgent.addUseBuildRecord(build, 0)
        action.ok()
    }

    protected async toChairEat(action) {
        await this.toChairAct(action, this.sitEat)
    }

    // 凳子喝饮料
    protected async toChairDrink(action) {
        await this.toChairAct(action, this.sitDrink)
    }

    // 凳子坐着
    protected async toChairSit(action) {
        await this.toChairAct(action, this.sit)
    }

    // 坐在凳子表演
    protected async toChairPlayAct(action) {
        await this.toChairAct(action, this.sitPlayAct)
    }

    protected async toChairSleep(action) {
        await this.toChairAct(action, this.sitSleep)
    }

    private async toChairAct(action, act) {
        let params = action.params || {}
        let { build } = params
        if (!build) {
            build = this.getUseChair()
        }
        if (!build) {
            console.warn("toChairAct not found", build)
            return action.ok()
        }

        let index = 0
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.moveToBuild, { build, paths: [{ index: 0 }] })
        await action.run(act, Object.assign(params, { build }))
        action.onTerminate()
        action.ok()
    }

    private getUseChair(isPlay = true) {
        return this.getUseBuild(this.carriage.getChairs(), isPlay)
    }
    //----------------------------------------------


    //-------------- 基础行为 --------------------
    //------------------------------------------
}